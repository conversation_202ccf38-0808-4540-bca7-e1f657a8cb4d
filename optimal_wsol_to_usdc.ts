import {
    Connection,
    PublicKey,
    Keypair,
    Transaction,
    sendAndConfirmTransaction,
    LAMPORTS_PER_SOL,
    ComputeBudgetProgram
} from "@solana/web3.js";
import {
    TOKEN_PROGRAM_ID,
    getAssociatedTokenAddress,
    createAssociatedTokenAccountInstruction,
    getAccount
} from "@solana/spl-token";
import DLMM from "@meteora-ag/dlmm";
import { BN } from '@project-serum/anchor';
import fs from "fs";

class OptimalWsolToUsdcSwap {
    private connection: Connection;
    private wallet: Keypair;
    
    // Optimal pool configuration based on bin analysis
    private optimalPool = {
        address: "BVRbyLjjfSBcoyiYFuxbgKYnWuiFaF9CSXEa5vdSZ9Hh", // Pool C (Step 20)
        name: "Pool C (Step 20)",
        targetBin: -844, // Best rate bin: $185.20 USDC per wSOL
        expectedRate: 185.20
    };
    
    // Token addresses
    private readonly USDC_MINT = new PublicKey("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v");
    private readonly WSOL_MINT = new PublicKey("So11111111111111111111111111111111111111112");
    
    constructor() {
        this.connection = this.createConnection();
        this.wallet = this.loadWallet();
    }
    
    private createConnection(): Connection {
        const shyftEndpoint = "https://rpc.shyft.to?api_key=sIOL8wBYn4MRCGaz";
        console.log(`🔄 Using Shyft RPC for optimal performance`);
        
        return new Connection(shyftEndpoint, {
            commitment: 'confirmed',
            confirmTransactionInitialTimeout: 60000,
        });
    }
    
    private loadWallet(): Keypair {
        const possiblePaths = [
            '/Users/<USER>/.config/solana/id.json',
            '/Users/<USER>/.config/solana/devnet.json',
            '/Users/<USER>/.config/solana/mainnet.json',
            './wallet.json',
            './keypair.json',
            './id.json',
            'arbitrage-wallet.json'
        ];

        for (const walletPath of possiblePaths) {
            try {
                if (fs.existsSync(walletPath)) {
                    console.log(`🔑 Loading wallet from: ${walletPath}`);
                    const secretKey = JSON.parse(fs.readFileSync(walletPath, 'utf8'));
                    const wallet = Keypair.fromSecretKey(new Uint8Array(secretKey));
                    console.log(`✅ Loaded wallet: ${wallet.publicKey.toString()}`);
                    return wallet;
                }
            } catch (error) {
                console.log(`⚠️ Failed to load wallet from ${walletPath}`);
                continue;
            }
        }

        throw new Error("❌ No wallet found! Please ensure you have a wallet file.");
    }
    
    async checkWalletBalances(): Promise<{ wsolBalance: number, usdcBalance: number, solBalance: number }> {
        console.log("\n💰 CHECKING WALLET BALANCES");
        console.log("═".repeat(50));
        
        // Check SOL balance
        const solLamports = await this.connection.getBalance(this.wallet.publicKey);
        const solBalance = solLamports / LAMPORTS_PER_SOL;
        console.log(`💎 SOL Balance: ${solBalance.toFixed(4)} SOL`);
        
        // Check wSOL balance
        let wsolBalance = 0;
        try {
            const wsolAccount = await getAssociatedTokenAddress(this.WSOL_MINT, this.wallet.publicKey);
            const wsolAccountInfo = await getAccount(this.connection, wsolAccount);
            wsolBalance = Number(wsolAccountInfo.amount) / 1e9;
            console.log(`🟡 wSOL Balance: ${wsolBalance.toFixed(6)} wSOL`);
        } catch (error) {
            console.log(`🟡 wSOL Balance: 0.000000 wSOL (account not found)`);
        }
        
        // Check USDC balance
        let usdcBalance = 0;
        try {
            const usdcAccount = await getAssociatedTokenAddress(this.USDC_MINT, this.wallet.publicKey);
            const usdcAccountInfo = await getAccount(this.connection, usdcAccount);
            usdcBalance = Number(usdcAccountInfo.amount) / 1e6;
            console.log(`🟢 USDC Balance: $${usdcBalance.toFixed(2)} USDC`);
        } catch (error) {
            console.log(`🟢 USDC Balance: $0.00 USDC (account not found)`);
        }
        
        return { wsolBalance, usdcBalance, solBalance };
    }
    
    async executeOptimalSwap(): Promise<void> {
        console.log("\n🎯 OPTIMAL wSOL → USDC SWAP EXECUTION");
        console.log(`📊 Using ${this.optimalPool.name} for best rate`);
        console.log(`💰 Target Rate: $${this.optimalPool.expectedRate} USDC per wSOL`);
        console.log("═".repeat(60));
        
        // Check balances
        const balances = await this.checkWalletBalances();
        
        if (balances.wsolBalance === 0) {
            console.log("❌ No wSOL balance found! Cannot execute swap.");
            console.log("💡 You need wSOL to swap for USDC.");
            return;
        }
        
        if (balances.solBalance < 0.01) {
            console.log("⚠️ Low SOL balance for transaction fees!");
            console.log(`💰 Current SOL: ${balances.solBalance.toFixed(4)} SOL`);
        }
        
        console.log(`\n🔄 Swapping ${balances.wsolBalance.toFixed(6)} wSOL → USDC`);
        console.log(`💰 Expected USDC: ~$${(balances.wsolBalance * this.optimalPool.expectedRate).toFixed(2)}`);
        
        try {
            // Connect to optimal pool
            console.log(`\n📊 Connecting to ${this.optimalPool.name}...`);
            const pool = await DLMM.create(this.connection, new PublicKey(this.optimalPool.address));
            console.log(`✅ Connected to pool successfully`);
            
            // Get current pool state
            const activeBin = await pool.getActiveBin();
            const activeBinId = Number(activeBin.binId);
            const activeBinPrice = Number(pool.fromPricePerLamport(Number(activeBin.price)));
            
            console.log(`🎯 Pool Active Bin: ${activeBinId} at $${activeBinPrice.toFixed(6)}`);
            console.log(`📍 Target Bin: ${this.optimalPool.targetBin} (${Math.abs(this.optimalPool.targetBin - activeBinId)} bins away)`);
            
            // Prepare swap amount
            const swapAmount = new BN(Math.floor(balances.wsolBalance * 1e9)); // Convert to lamports
            console.log(`💱 Swap Amount: ${balances.wsolBalance.toFixed(6)} wSOL (${swapAmount.toString()} lamports)`);
            
            // Get bin arrays for swap
            const binArrays = await pool.getBinArrayForSwap(false); // false = wSOL -> USDC
            console.log(`📊 Found ${binArrays.length} bin arrays for swap`);
            
            // Create swap quote
            const swapQuote = pool.swapQuote(
                swapAmount,
                false, // swapForY = false (wSOL -> USDC)
                new BN(1000), // 10% slippage tolerance (increased for small amounts)
                binArrays
            );
            
            const expectedUSDC = Number(swapQuote.outAmount) / 1e6;
            const actualRate = expectedUSDC / balances.wsolBalance;
            const priceImpact = swapQuote.priceImpact;

            console.log(`\n📊 SWAP QUOTE ANALYSIS:`);
            console.log(`💰 Input: ${balances.wsolBalance.toFixed(6)} wSOL`);
            console.log(`💰 Output: $${expectedUSDC.toFixed(2)} USDC`);
            console.log(`📈 Actual Rate: $${actualRate.toFixed(2)} USDC per wSOL`);
            console.log(`📊 Price Impact: ${priceImpact.toFixed(4)}%`);
            console.log(`💸 Fee: ${(Number(swapQuote.fee) / 1e9).toFixed(6)} wSOL`);

            // Debug swap quote details
            console.log(`\n🔍 DEBUG INFO:`);
            console.log(`   Input Amount: ${swapQuote.consumedInAmount.toString()} lamports`);
            console.log(`   Output Amount: ${swapQuote.outAmount.toString()} micro USDC`);
            console.log(`   Min Output: ${swapQuote.minOutAmount.toString()} micro USDC`);

            // Check if rate is reasonable (should be around $185)
            if (actualRate > 1000 || actualRate < 50) {
                console.log(`❌ UNREALISTIC RATE DETECTED: $${actualRate.toFixed(2)}`);
                console.log(`💡 Expected rate around $185, got $${actualRate.toFixed(2)}`);
                console.log(`🔄 This suggests a swap direction or calculation issue`);
                return;
            }

            // Rate comparison
            const rateDifference = actualRate - this.optimalPool.expectedRate;
            const ratePercentage = (rateDifference / this.optimalPool.expectedRate) * 100;

            if (rateDifference >= 0) {
                console.log(`✅ Rate is ${rateDifference.toFixed(2)} better than expected (+${ratePercentage.toFixed(2)}%)`);
            } else {
                console.log(`⚠️ Rate is ${Math.abs(rateDifference).toFixed(2)} worse than expected (${ratePercentage.toFixed(2)}%)`);
            }
            
            // Confirm execution
            console.log(`\n🚀 EXECUTING OPTIMAL SWAP...`);
            
            // Ensure USDC account exists
            await this.ensureUSDCAccount();
            
            // Create swap transaction
            const swapTx = await pool.swap({
                inToken: pool.tokenX.publicKey, // wSOL
                outToken: pool.tokenY.publicKey, // USDC
                inAmount: swapAmount,
                minOutAmount: swapQuote.minOutAmount,
                lbPair: pool.pubkey,
                user: this.wallet.publicKey,
                binArraysPubkey: swapQuote.binArraysPubkey,
            });
            
            // Add compute budget
            const transaction = new Transaction();
            transaction.add(ComputeBudgetProgram.setComputeUnitLimit({ units: 300000 }));
            transaction.add(ComputeBudgetProgram.setComputeUnitPrice({ microLamports: 15000 }));
            transaction.add(...swapTx.instructions);
            
            console.log(`📝 Transaction created with ${transaction.instructions.length} instructions`);
            
            // Execute transaction
            const signature = await sendAndConfirmTransaction(
                this.connection,
                transaction,
                [this.wallet],
                { skipPreflight: false }
            );
            
            console.log(`\n🎉 SWAP SUCCESSFUL!`);
            console.log(`🔗 Transaction: ${signature}`);
            console.log(`💰 Swapped: ${balances.wsolBalance.toFixed(6)} wSOL → $${expectedUSDC.toFixed(2)} USDC`);
            console.log(`📈 Rate Achieved: $${actualRate.toFixed(2)} USDC per wSOL`);
            console.log(`✅ Optimal strategy executed successfully!`);
            
            // Check final balances
            console.log(`\n🔄 Checking final balances...`);
            setTimeout(async () => {
                await this.checkWalletBalances();
            }, 3000);
            
        } catch (error) {
            console.log(`❌ Swap execution failed: ${error instanceof Error ? error.message : String(error)}`);
            console.log(`💡 This could be due to:`)
            console.log(`   - Insufficient liquidity in target bin`);
            console.log(`   - Price movement during execution`);
            console.log(`   - Network congestion`);
        }
    }
    
    private async ensureUSDCAccount(): Promise<void> {
        try {
            const usdcAccount = await getAssociatedTokenAddress(this.USDC_MINT, this.wallet.publicKey);
            await getAccount(this.connection, usdcAccount);
            console.log(`✅ USDC account exists`);
        } catch (error) {
            console.log(`📋 Creating USDC account...`);
            const usdcAccount = await getAssociatedTokenAddress(this.USDC_MINT, this.wallet.publicKey);
            const createAccountIx = createAssociatedTokenAccountInstruction(
                this.wallet.publicKey,
                usdcAccount,
                this.wallet.publicKey,
                this.USDC_MINT
            );
            
            const transaction = new Transaction().add(createAccountIx);
            await sendAndConfirmTransaction(this.connection, transaction, [this.wallet]);
            console.log(`✅ USDC account created`);
        }
    }
    
    async run(): Promise<void> {
        try {
            console.log("🎯 OPTIMAL wSOL → USDC SWAP STRATEGY");
            console.log("💰 Using wallet balance for maximum USDC conversion");
            console.log("📊 Targeting Pool C Bin -844 for best rate");
            console.log("═".repeat(60));
            
            await this.executeOptimalSwap();
            
        } catch (error) {
            console.error("❌ Script execution failed:", error instanceof Error ? error.message : String(error));
        }
    }
}

// Execute the optimal swap
const swapper = new OptimalWsolToUsdcSwap();
swapper.run().catch(console.error);
