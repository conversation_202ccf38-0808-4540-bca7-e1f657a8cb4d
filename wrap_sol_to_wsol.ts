import { Connection, PublicKey, Keypair, Transaction, sendAndConfirmTransaction, SystemProgram } from '@solana/web3.js';
import { getAssociatedTokenAddress, createAssociatedTokenAccountInstruction, createSyncNativeInstruction, NATIVE_MINT, getAccount } from '@solana/spl-token';
import * as fs from 'fs';

const WSOL_MINT = new PublicKey("So11111111111111111111111111111111111111112");

async function wrapSolToWsol() {
    console.log("🔄 WRAPPING 0.005 SOL TO wSOL");
    console.log("═".repeat(50));
    
    // Load wallet
    const walletPath = '/Users/<USER>/.config/solana/id.json';
    const secretKey = JSON.parse(fs.readFileSync(walletPath, 'utf8'));
    const wallet = Keypair.fromSecretKey(new Uint8Array(secretKey));
    
    console.log(`🔑 Wallet: ${wallet.publicKey.toString()}`);
    
    // Connect to mainnet
    const connection = new Connection("https://api.mainnet-beta.solana.com", 'confirmed');
    
    // Check SOL balance
    const solBalance = await connection.getBalance(wallet.publicKey);
    const solBalanceSOL = solBalance / 1e9;
    console.log(`💰 SOL Balance: ${solBalanceSOL.toFixed(6)} SOL`);
    
    const wrapAmount = 0.01; // 0.005 SOL to wrap
    const wrapAmountLamports = wrapAmount * 1e9;
    
    if (solBalanceSOL < wrapAmount + 0.002) {
        console.log(`❌ Insufficient SOL. Need: ${wrapAmount + 0.002} SOL, Have: ${solBalanceSOL} SOL`);
        return;
    }
    
    console.log(`✅ Sufficient SOL for wrapping: ${wrapAmount} SOL + fees`);
    
    try {
        // Get wSOL account address
        const wsolAccount = await getAssociatedTokenAddress(WSOL_MINT, wallet.publicKey);
        console.log(`📍 wSOL Account: ${wsolAccount.toString()}`);
        
        // Check if wSOL account exists
        let needsAccountCreation = false;
        try {
            await getAccount(connection, wsolAccount);
            console.log("✅ wSOL account already exists");
        } catch (error) {
            console.log("📋 wSOL account doesn't exist - will create");
            needsAccountCreation = true;
        }
        
        // Create transaction
        const transaction = new Transaction();
        
        console.log("\n🏗️ Building wrapping transaction...");
        
        // Step 1: Create wSOL account if needed
        if (needsAccountCreation) {
            console.log("1️⃣ Creating wSOL account...");
            const createWsolAccountIx = createAssociatedTokenAccountInstruction(
                wallet.publicKey,
                wsolAccount,
                wallet.publicKey,
                WSOL_MINT
            );
            transaction.add(createWsolAccountIx);
        } else {
            console.log("1️⃣ wSOL account exists - skipping creation");
        }
        
        // Step 2: Transfer SOL to wSOL account
        console.log("2️⃣ Transferring SOL to wSOL account...");
        const transferIx = SystemProgram.transfer({
            fromPubkey: wallet.publicKey,
            toPubkey: wsolAccount,
            lamports: wrapAmountLamports
        });
        transaction.add(transferIx);
        
        // Step 3: Sync native (wrap SOL to wSOL)
        console.log("3️⃣ Syncing native (wrapping SOL to wSOL)...");
        const syncIx = createSyncNativeInstruction(wsolAccount);
        transaction.add(syncIx);
        
        // Set transaction details
        const { blockhash } = await connection.getLatestBlockhash('confirmed');
        transaction.recentBlockhash = blockhash;
        transaction.feePayer = wallet.publicKey;
        
        console.log(`\n📋 Transaction built with ${transaction.instructions.length} instructions`);
        
        // Simulate transaction
        console.log("🧪 Simulating SOL wrapping...");
        const simulation = await connection.simulateTransaction(transaction);
        
        if (simulation.value.err) {
            console.log("❌ Simulation failed:", simulation.value.err);
            console.log("📋 Logs:", simulation.value.logs);
            return;
        }
        
        console.log("✅ Simulation successful!");
        console.log(`⛽ Compute units used: ${simulation.value.unitsConsumed}`);
        
        // Execute the transaction
        console.log("\n🚀 Executing SOL wrapping on mainnet...");
        
        const signature = await sendAndConfirmTransaction(
            connection,
            transaction,
            [wallet],
            { commitment: 'confirmed' }
        );
        
        console.log("🎉 SUCCESS! SOL wrapped to wSOL!");
        console.log(`📝 Transaction Signature: ${signature}`);
        console.log(`🔗 Explorer: https://solscan.io/tx/${signature}`);
        
        // Check final balances
        console.log("\n📊 Checking final balances...");
        
        const finalSolBalance = await connection.getBalance(wallet.publicKey);
        const finalSolBalanceSOL = finalSolBalance / 1e9;
        
        console.log(`💰 Final SOL Balance: ${finalSolBalanceSOL.toFixed(6)} SOL`);
        console.log(`📊 SOL Used: ${(solBalanceSOL - finalSolBalanceSOL).toFixed(6)} SOL`);
        
        try {
            const finalWsolBalance = await getAccount(connection, wsolAccount);
            const wsolAmount = Number(finalWsolBalance.amount) / 1e9;
            console.log(`💰 wSOL Balance: ${wsolAmount.toFixed(6)} wSOL`);
            console.log(`💵 wSOL Value: ~$${(wsolAmount * 190).toFixed(2)}`);
            
            if (wsolAmount >= wrapAmount * 0.99) { // Allow for small rounding
                console.log("\n✅ SOL WRAPPING SUCCESSFUL!");
                console.log("🎯 wSOL is ready for arbitrage swaps");
                console.log("💰 You now have wrapped SOL that can be traded");
            } else {
                console.log("⚠️ wSOL amount is less than expected");
            }
            
        } catch (error) {
            console.log("❌ Error reading wSOL balance:", error.message);
        }
        
    } catch (error) {
        console.log("❌ Error:", error.message);
        if (error.logs) {
            console.log("📋 Transaction logs:", error.logs);
        }
    }
}

// Run the wrapping
wrapSolToWsol().catch(console.error);
