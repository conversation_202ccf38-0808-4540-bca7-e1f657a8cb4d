import {
    Connection,
    PublicKey,
} from "@solana/web3.js";
import DLMM from "@meteora-ag/dlmm";

interface BinLiquidityData {
    binId: number;
    price: number;
    liquidityX: number; // wSOL
    liquidityY: number; // USDC
    distanceFromActive: number;
    liquidityValue: number; // Total USD value
    canBuyFrom: boolean;
    canSellTo: boolean;
}

interface PoolBinAnalysis {
    poolName: string;
    poolAddress: string;
    activeBinId: number;
    activeBinPrice: number;
    bins: BinLiquidityData[];
}

class BinLiquidityAnalyzer {
    private connection: Connection;
    
    // Pool addresses from the flash arbitrage script
    private poolConfigs = [
        { address: "BVRbyLjjfSBcoyiYFuxbgKYnWuiFaF9CSXEa5vdSZ9Hh", name: "Pool C (Step 20)" },
        { address: "BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y", name: "Pool D (Step 10)" },
        { address: "2sf5NYcY4zUPXUSmG6f66mskb24t5F8S11pC1Nz5nQT3", name: "Pool E (Step 100)" },
        { address: "9Q1njS4j8svdjCnGd2xJn7RAkqrJ2vqjaPs3sXRZ6UR7", name: "Pool F (New)" }
    ];

    constructor() {
        // Use Shyft API for reliable connection
        const shyftEndpoint = "https://rpc.shyft.to?api_key=sIOL8wBYn4MRCGaz";
        this.connection = new Connection(shyftEndpoint, {
            commitment: 'confirmed',
            confirmTransactionInitialTimeout: 60000,
        });
    }

    async analyzeBinLiquidity(): Promise<void> {
        console.log("🔍 ANALYZING BIN LIQUIDITY AROUND ACTIVE BINS");
        console.log("📊 Getting 15 bins with liquidity around active bin for each pool");
        console.log("═".repeat(80));

        const poolAnalyses: PoolBinAnalysis[] = [];

        for (const config of this.poolConfigs) {
            try {
                console.log(`\n📋 Analyzing ${config.name}...`);
                
                // Connect to the pool
                const pool = await DLMM.create(this.connection, new PublicKey(config.address));
                
                // Get active bin
                const activeBin = await pool.getActiveBin();
                const activeBinId = Number(activeBin.binId);
                const activeBinPrice = Number(pool.fromPricePerLamport(Number(activeBin.price)));
                
                console.log(`   🎯 Active Bin: ${activeBinId} at $${activeBinPrice.toFixed(6)}`);
                
                // Get bins around active (7 below, active, 7 above)
                const binsToAnalyze: BinLiquidityData[] = [];
                
                for (let offset = -7; offset <= 7; offset++) {
                    const targetBinId = activeBinId + offset;
                    
                    try {
                        // Get bin data using the correct DLMM method
                        const { bins } = await pool.getBinsBetweenLowerAndUpperBound(targetBinId, targetBinId);

                        if (bins.length === 0) {
                            throw new Error(`No bin found for ID ${targetBinId}`);
                        }

                        const binData = bins[0];
                        const binPrice = Number(binData.price);
                        const liquidityX = Number(binData.xAmount) / 1e9; // wSOL
                        const liquidityY = Number(binData.yAmount) / 1e6; // USDC
                        const liquidityValue = (liquidityX * binPrice) + liquidityY;
                        
                        binsToAnalyze.push({
                            binId: targetBinId,
                            price: binPrice,
                            liquidityX,
                            liquidityY,
                            distanceFromActive: offset,
                            liquidityValue,
                            canBuyFrom: liquidityX > 0.01, // At least 0.01 wSOL
                            canSellTo: liquidityY > 10 // At least $10 USDC
                        });
                        
                    } catch (error) {
                        // Bin might not exist or have no liquidity
                        binsToAnalyze.push({
                            binId: targetBinId,
                            price: 0,
                            liquidityX: 0,
                            liquidityY: 0,
                            distanceFromActive: offset,
                            liquidityValue: 0,
                            canBuyFrom: false,
                            canSellTo: false
                        });
                    }
                }
                
                poolAnalyses.push({
                    poolName: config.name,
                    poolAddress: config.address,
                    activeBinId,
                    activeBinPrice,
                    bins: binsToAnalyze
                });
                
                console.log(`   ✅ Analyzed 15 bins around active bin`);
                
            } catch (error) {
                console.log(`   ❌ Error analyzing ${config.name}: ${error instanceof Error ? error.message : String(error)}`);
            }
        }

        // Display results in table format
        this.displayBinLiquidityTable(poolAnalyses);
    }

    private displayBinLiquidityTable(poolAnalyses: PoolBinAnalysis[]): void {
        console.log("\n📊 BIN LIQUIDITY ANALYSIS TABLE");
        console.log("═".repeat(120));

        for (const analysis of poolAnalyses) {
            console.log(`\n🏊 ${analysis.poolName} - Active Bin: ${analysis.activeBinId} ($${analysis.activeBinPrice.toFixed(6)})`);
            console.log("─".repeat(120));
            console.log("│ Bin ID │ Distance │    Price ($)    │  wSOL Liq  │  USDC Liq  │ Total Value │ Buy │ Sell │");
            console.log("├────────┼──────────┼─────────────────┼────────────┼────────────┼─────────────┼─────┼──────┤");

            for (const bin of analysis.bins) {
                const binIdStr = bin.binId.toString().padStart(6);
                const distanceStr = bin.distanceFromActive.toString().padStart(8);
                const priceStr = bin.price > 0 ? bin.price.toFixed(6).padStart(15) : "No Price".padStart(15);
                const wsolStr = bin.liquidityX > 0 ? bin.liquidityX.toFixed(4).padStart(10) : "0.0000".padStart(10);
                const usdcStr = bin.liquidityY > 0 ? bin.liquidityY.toFixed(2).padStart(10) : "0.00".padStart(10);
                const valueStr = bin.liquidityValue > 0 ? `$${bin.liquidityValue.toFixed(2)}`.padStart(11) : "$0.00".padStart(11);
                const buyStr = bin.canBuyFrom ? " ✅ " : " ❌ ";
                const sellStr = bin.canSellTo ? " ✅ " : " ❌ ";
                
                // Highlight active bin
                const prefix = bin.distanceFromActive === 0 ? "│*" : "│ ";
                const suffix = bin.distanceFromActive === 0 ? "*│" : " │";
                
                console.log(`${prefix}${binIdStr} │${distanceStr} │${priceStr} │${wsolStr} │${usdcStr} │${valueStr} │${buyStr}│${sellStr}${suffix}`);
            }
            
            console.log("└────────┴──────────┴─────────────────┴────────────┴────────────┴─────────────┴─────┴──────┘");
            
            // Summary statistics
            const totalBins = analysis.bins.length;
            const binsWithLiquidity = analysis.bins.filter(b => b.liquidityValue > 0).length;
            const buyableBins = analysis.bins.filter(b => b.canBuyFrom).length;
            const sellableBins = analysis.bins.filter(b => b.canSellTo).length;
            const totalValue = analysis.bins.reduce((sum, b) => sum + b.liquidityValue, 0);
            
            console.log(`📊 Summary: ${binsWithLiquidity}/${totalBins} bins with liquidity | ${buyableBins} buyable | ${sellableBins} sellable | Total: $${totalValue.toFixed(2)}`);
        }

        // Cross-pool comparison
        this.displayCrossPoolComparison(poolAnalyses);
    }

    private displayCrossPoolComparison(poolAnalyses: PoolBinAnalysis[]): void {
        console.log("\n🔄 CROSS-POOL COMPARISON");
        console.log("═".repeat(80));

        // Find price ranges for each pool
        for (const analysis of poolAnalyses) {
            const binsWithPrice = analysis.bins.filter(b => b.price > 0);
            if (binsWithPrice.length === 0) continue;

            const minPrice = Math.min(...binsWithPrice.map(b => b.price));
            const maxPrice = Math.max(...binsWithPrice.map(b => b.price));
            const avgPrice = binsWithPrice.reduce((sum, b) => sum + b.price, 0) / binsWithPrice.length;
            const priceSpread = maxPrice - minPrice;
            
            console.log(`📊 ${analysis.poolName}:`);
            console.log(`   💰 Price Range: $${minPrice.toFixed(6)} - $${maxPrice.toFixed(6)}`);
            console.log(`   📈 Average Price: $${avgPrice.toFixed(6)}`);
            console.log(`   📊 Price Spread: $${priceSpread.toFixed(6)} (${((priceSpread/avgPrice)*100).toFixed(2)}%)`);
            console.log(`   🎯 Active Bin Price: $${analysis.activeBinPrice.toFixed(6)}`);
        }

        // Find arbitrage opportunities
        console.log("\n💰 POTENTIAL ARBITRAGE OPPORTUNITIES:");
        console.log("─".repeat(60));

        for (let i = 0; i < poolAnalyses.length; i++) {
            for (let j = i + 1; j < poolAnalyses.length; j++) {
                const pool1 = poolAnalyses[i];
                const pool2 = poolAnalyses[j];
                
                const priceDiff = Math.abs(pool1.activeBinPrice - pool2.activeBinPrice);
                const avgPrice = (pool1.activeBinPrice + pool2.activeBinPrice) / 2;
                const profitPercent = (priceDiff / avgPrice) * 100;
                
                if (profitPercent > 0.1) {
                    const cheaperPool = pool1.activeBinPrice < pool2.activeBinPrice ? pool1 : pool2;
                    const expensivePool = pool1.activeBinPrice > pool2.activeBinPrice ? pool1 : pool2;
                    
                    console.log(`🔄 ${cheaperPool.poolName} → ${expensivePool.poolName}:`);
                    console.log(`   📊 Price Difference: $${priceDiff.toFixed(6)} (${profitPercent.toFixed(3)}%)`);
                    console.log(`   🟢 Buy: $${cheaperPool.activeBinPrice.toFixed(6)}`);
                    console.log(`   🔴 Sell: $${expensivePool.activeBinPrice.toFixed(6)}`);
                }
            }
        }
    }

    async run(): Promise<void> {
        try {
            await this.analyzeBinLiquidity();
        } catch (error) {
            console.error("❌ Analysis failed:", error instanceof Error ? error.message : String(error));
        }
    }
}

// Run the analyzer
const analyzer = new BinLiquidityAnalyzer();
analyzer.run().catch(console.error);
